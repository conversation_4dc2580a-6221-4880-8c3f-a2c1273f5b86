@import "tailwindcss";
@plugin "@tailwindcss/typography";

@custom-variant dark (&:where(.dark, .dark *));

/* CSS Variables for theming */
:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --primary: 221.2 83.2% 53.3%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96%;
  --secondary-foreground: 222.2 84% 4.9%;
  --muted: 210 40% 96%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96%;
  --accent-foreground: 222.2 84% 4.9%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 221.2 83.2% 53.3%;
  --radius: 0.5rem;
}

.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;
  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;
  --primary: 217.2 91.2% 59.8%;
  --primary-foreground: 222.2 84% 4.9%;
  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;
  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 224.3 76.3% 94.1%;
}

/* Shiki theme switching */
html:not(.dark) .shiki,
html:not(.dark) .shiki span {
  color: var(--shiki-light) !important;
  background-color: var(--shiki-light-bg) !important;
  /* Optional, if you also want font styles */
  font-style: var(--shiki-light-font-style) !important;
  font-weight: var(--shiki-light-font-weight) !important;
  text-decoration: var(--shiki-light-text-decoration) !important;
}

html.dark .shiki,
html.dark .shiki span {
  color: var(--shiki-dark) !important;
  background-color: var(--shiki-dark-bg) !important;
  /* Optional, if you also want font styles */
  font-style: var(--shiki-dark-font-style) !important;
  font-weight: var(--shiki-dark-font-weight) !important;
  text-decoration: var(--shiki-dark-text-decoration) !important;
}

/* Ensure code blocks have proper styling */
.shiki {
  overflow-x: auto;
  border-radius: 0;
  margin: 0;
  padding: 1rem;
  font-family: 'Fira Code', 'Monaco', 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
}

.shiki code {
  background: transparent !important;
  padding: 0 !important;
  border-radius: 0 !important;
  font-size: inherit !important;
  color: inherit !important;
}

/* Scrollbar styling for code blocks */
.shiki::-webkit-scrollbar {
  height: 8px;
}

.shiki::-webkit-scrollbar-track {
  background: transparent;
}

.shiki::-webkit-scrollbar-thumb {
  background: hsl(var(--border));
  border-radius: 4px;
}

.shiki::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground));
}

/* Ensure proper theme switching for site components */
.not-prose .shiki,
.not-prose .shiki span {
  color: var(--shiki-light) !important;
  background-color: var(--shiki-light-bg) !important;
  font-style: var(--shiki-light-font-style) !important;
  font-weight: var(--shiki-light-font-weight) !important;
  text-decoration: var(--shiki-light-text-decoration) !important;
}

.dark .not-prose .shiki,
.dark .not-prose .shiki span {
  color: var(--shiki-dark) !important;
  background-color: var(--shiki-dark-bg) !important;
  font-style: var(--shiki-dark-font-style) !important;
  font-weight: var(--shiki-dark-font-weight) !important;
  text-decoration: var(--shiki-dark-text-decoration) !important;
}

/* Force override any conflicting styles */
.not-prose .shiki pre,
.not-prose .shiki code {
  background: transparent !important;
  color: inherit !important;
}

* {
  box-sizing: border-box;
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  html {
    --scrollbar-color: #00000032;
    -webkit-tap-highlight-color: transparent;
    scrollbar-gutter: stable;
    scrollbar-color: var(--scrollbar-color) transparent;
    scrollbar-width: thin;
  }

  html.dark {
    --scrollbar-color: #ffffff32;
  }

  .thin-scroll {
    -webkit-tap-highlight-color: transparent;
    scrollbar-gutter: stable;
    scrollbar-color: #ffffff32 transparent;
    scrollbar-width: thin;
  }

}

@theme {
  --default-mono-font-family: "Geist Mono", ui-monospace, SFMono-Regular,
    Roboto Mono, Menlo, Monaco, Liberation Mono, DejaVu Sans Mono, Courier New,
    monospace;
  --font-mono: "Geist Mono", ui-monospace, SFMono-Regular, Roboto Mono, Menlo,
    Monaco, Liberation Mono, DejaVu Sans Mono, Courier New, monospace;
  --default-mono-font-feature-settings: normal;
  --default-mono-font-variation-settings: normal;

  /* Color palette for markdown elements */
  --color-background: hsl(var(--background));
  --color-foreground: hsl(var(--foreground));
  --color-card: hsl(var(--card));
  --color-card-foreground: hsl(var(--card-foreground));
  --color-popover: hsl(var(--popover));
  --color-popover-foreground: hsl(var(--popover-foreground));
  --color-primary: hsl(var(--primary));
  --color-primary-foreground: hsl(var(--primary-foreground));
  --color-secondary: hsl(var(--secondary));
  --color-secondary-foreground: hsl(var(--secondary-foreground));
  --color-muted: hsl(var(--muted));
  --color-muted-foreground: hsl(var(--muted-foreground));
  --color-accent: hsl(var(--accent));
  --color-accent-foreground: hsl(var(--accent-foreground));
  --color-destructive: hsl(var(--destructive));
  --color-destructive-foreground: hsl(var(--destructive-foreground));
  --color-border: hsl(var(--border));
  --color-input: hsl(var(--input));
  --color-ring: hsl(var(--ring));
}
/* Grid background with light/dark mode and more visible lines */
.grid-bg {
  background-image:
    linear-gradient(to right, var(--grid-line-color, rgba(34,34,34,0.03)) 1px, transparent 1px),
    linear-gradient(to bottom, var(--grid-line-color, rgba(34,34,34,0.03)) 1px, transparent 1px);
  background-size: 24px 24px;
}
.light .grid-bg {
  --grid-line-color: rgba(34,34,34,0.03);
}
.dark .grid-bg {
  --grid-line-color: rgba(229,231,235,0.015);
}

/* Enhanced grid background for hero sections */
.grid-bg-hero {
  background-image:
    linear-gradient(to right, var(--grid-line-color-hero, rgba(34,34,34,0.04)) 1px, transparent 1px),
    linear-gradient(to bottom, var(--grid-line-color-hero, rgba(34,34,34,0.04)) 1px, transparent 1px);
  background-size: 32px 32px;
}
.light .grid-bg-hero {
  --grid-line-color-hero: rgba(34,34,34,0.04);
}
.dark .grid-bg-hero {
  --grid-line-color-hero: rgba(229,231,235,0.025);
}

/* Custom range input styling for the gradient generator */
input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  cursor: pointer;
}

input[type="range"]::-webkit-slider-track {
  background: #e5e7eb;
  height: 8px;
  border-radius: 4px;
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  background: #3b82f6;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

input[type="range"]::-webkit-slider-thumb:hover {
  background: #2563eb;
  transform: scale(1.1);
}

input[type="range"]::-moz-range-track {
  background: #e5e7eb;
  height: 8px;
  border-radius: 4px;
  border: none;
}

input[type="range"]::-moz-range-thumb {
  background: #3b82f6;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

input[type="range"]::-moz-range-thumb:hover {
  background: #2563eb;
}

/* Ensure proper focus states */
input[type="range"]:focus {
  outline: none;
}

input[type="range"]:focus::-webkit-slider-thumb {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

input[type="range"]:focus::-moz-range-thumb {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

/* Enhanced slider styles for color palette */
.slider {
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  cursor: pointer;
}

.slider::-webkit-slider-track {
  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899);
  height: 12px;
  border-radius: 6px;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  height: 24px;
  width: 24px;
  border-radius: 50%;
  background: #ffffff;
  border: 3px solid #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  cursor: pointer;
  transition: all 0.2s ease;
}

.slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
}

.slider::-moz-range-track {
  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899);
  height: 12px;
  border-radius: 6px;
  border: none;
}

.slider::-moz-range-thumb {
  height: 24px;
  width: 24px;
  border-radius: 50%;
  background: #ffffff;
  border: 3px solid #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  cursor: pointer;
  transition: all 0.2s ease;
}

.slider::-moz-range-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
}

/* Dark mode slider adjustments */
.dark .slider::-webkit-slider-thumb {
  background: #1f2937;
  border: 3px solid #60a5fa;
}

.dark .slider::-moz-range-thumb {
  background: #1f2937;
  border: 3px solid #60a5fa;
}

