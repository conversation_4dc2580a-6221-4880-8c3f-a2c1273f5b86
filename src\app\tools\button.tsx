import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>, <PERSON>, Moon } from 'lucide-react';
import './button-generator.css';

interface ButtonConfig {
  text: string;
  // Light mode colors
  backgroundColor: string;
  textColor: string;
  borderColor: string;
  hoverBackgroundColor: string;
  hoverTextColor: string;
  hoverBorderColor: string;
  // Dark mode colors
  darkBackgroundColor: string;
  darkTextColor: string;
  darkBorderColor: string;
  darkHoverBackgroundColor: string;
  darkHoverTextColor: string;
  darkHoverBorderColor: string;
  // Other properties
  borderWidth: number;
  borderStyle: string;
  borderRadius: number;
  width: number;
  height: number;
  paddingX: number;
  paddingY: number;
  fontSize: number;
  fontWeight: string;
  boxShadow: string;
  transition: string;
  supportDarkMode: boolean;
}

const ButtonGenerator: React.FC = () => {
  const [config, setConfig] = useState<ButtonConfig>({
    text: 'Click Me',
    // Light mode colors
    backgroundColor: '#3b82f6',
    textColor: '#ffffff',
    borderColor: '#3b82f6',
    hoverBackgroundColor: '#2563eb',
    hoverTextColor: '#ffffff',
    hoverBorderColor: '#2563eb',
    // Dark mode colors
    darkBackgroundColor: '#1e40af',
    darkTextColor: '#f1f5f9',
    darkBorderColor: '#1e40af',
    darkHoverBackgroundColor: '#1d4ed8',
    darkHoverTextColor: '#f1f5f9',
    darkHoverBorderColor: '#1d4ed8',
    // Other properties
    borderWidth: 2,
    borderStyle: 'solid',
    borderRadius: 8,
    width: 140,
    height: 44,
    paddingX: 24,
    paddingY: 12,
    fontSize: 16,
    fontWeight: '500',
    boxShadow: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px 0 rgb(0 0 0 / 0.06)',
    transition: 'all 0.2s ease-in-out',
    supportDarkMode: true,
  });

  const [exportFormat, setExportFormat] = useState('css');
  const [tailwindFormat, setTailwindFormat] = useState<'className' | 'class'>('className');
  const [copied, setCopied] = useState(false);
  const [previewMode, setPreviewMode] = useState<'light' | 'dark'>('light');
  const [isGenerating, setIsGenerating] = useState(false);

  const updateConfig = (key: keyof ButtonConfig, value: string | number | boolean) => {
    setConfig(prev => ({ ...prev, [key]: value }));
  };

  const getButtonStyle = (isHover: boolean = false, isDark: boolean = false): React.CSSProperties => {
    const colors = isDark ? {
      backgroundColor: isHover ? config.darkHoverBackgroundColor : config.darkBackgroundColor,
      color: isHover ? config.darkHoverTextColor : config.darkTextColor,
      borderColor: isHover ? config.darkHoverBorderColor : config.darkBorderColor,
    } : {
      backgroundColor: isHover ? config.hoverBackgroundColor : config.backgroundColor,
      color: isHover ? config.hoverTextColor : config.textColor,
      borderColor: isHover ? config.hoverBorderColor : config.borderColor,
    };

    return {
      ...colors,
      borderWidth: `${config.borderWidth}px`,
      borderStyle: config.borderStyle,
      borderRadius: `${config.borderRadius}px`,
      width: `${config.width}px`,
      height: `${config.height}px`,
      paddingLeft: `${config.paddingX}px`,
      paddingRight: `${config.paddingX}px`,
      paddingTop: `${config.paddingY}px`,
      paddingBottom: `${config.paddingY}px`,
      fontSize: `${config.fontSize}px`,
      fontWeight: config.fontWeight,
      boxShadow: config.boxShadow,
      transition: config.transition,
      cursor: 'pointer',
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      fontFamily: 'inherit',
      outline: 'none',
    };
  };

  const generateCSS = (): string => {
    const lightStyle = getButtonStyle(false, false);
    const lightHoverStyle = getButtonStyle(true, false);
    const darkStyle = getButtonStyle(false, true);
    const darkHoverStyle = getButtonStyle(true, true);
    
    let css = `.custom-button {
  background-color: ${lightStyle.backgroundColor};
  color: ${lightStyle.color};
  border: ${lightStyle.borderWidth} ${lightStyle.borderStyle} ${lightStyle.borderColor};
  border-radius: ${lightStyle.borderRadius};
  width: ${lightStyle.width};
  height: ${lightStyle.height};
  padding: ${lightStyle.paddingTop} ${lightStyle.paddingRight} ${lightStyle.paddingBottom} ${lightStyle.paddingLeft};
  font-size: ${lightStyle.fontSize};
  font-weight: ${lightStyle.fontWeight};
  box-shadow: ${lightStyle.boxShadow};
  transition: ${lightStyle.transition};
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-family: inherit;
  outline: none;
}

.custom-button:hover {
  background-color: ${lightHoverStyle.backgroundColor};
  color: ${lightHoverStyle.color};
  border-color: ${lightHoverStyle.borderColor};
}`;

    if (config.supportDarkMode) {
      css += `

@media (prefers-color-scheme: dark) {
  .custom-button {
    background-color: ${darkStyle.backgroundColor};
    color: ${darkStyle.color};
    border-color: ${darkStyle.borderColor};
  }
  
  .custom-button:hover {
    background-color: ${darkHoverStyle.backgroundColor};
    color: ${darkHoverStyle.color};
    border-color: ${darkHoverStyle.borderColor};
  }
}`;
    }

    return css;
  };

  const generateTailwindCSS = (): string => {
    const getColorClass = (color: string, prefix: string = '') => {
      return `${prefix}[${color}]`;
    };

    const borderStyleClass = config.borderStyle === 'solid' ? 'border-solid' : 
                           config.borderStyle === 'dashed' ? 'border-dashed' : 
                           config.borderStyle === 'dotted' ? 'border-dotted' : 'border-solid';

    const fontWeightClass = config.fontWeight === '300' ? 'font-light' :
                           config.fontWeight === '400' ? 'font-normal' :
                           config.fontWeight === '500' ? 'font-medium' :
                           config.fontWeight === '600' ? 'font-semibold' :
                           config.fontWeight === '700' ? 'font-bold' : 'font-medium';

    let classes = [
      `bg-${getColorClass(config.backgroundColor)}`,
      `text-${getColorClass(config.textColor)}`,
      `border-[${config.borderWidth}px]`,
      `border-${getColorClass(config.borderColor)}`,
      borderStyleClass,
      `rounded-[${config.borderRadius}px]`,
      `w-[${config.width}px]`,
      `h-[${config.height}px]`,
      `px-[${config.paddingX}px]`,
      `py-[${config.paddingY}px]`,
      `text-[${config.fontSize}px]`,
      fontWeightClass,
      `hover:bg-${getColorClass(config.hoverBackgroundColor)}`,
      `hover:text-${getColorClass(config.hoverTextColor)}`,
      `hover:border-${getColorClass(config.hoverBorderColor)}`,
      'inline-flex',
      'items-center',
      'justify-center',
      'cursor-pointer',
      'outline-none',
      'transition-all',
      'duration-200',
      'ease-in-out'
    ];

    if (config.supportDarkMode) {
      classes = classes.concat([
        `dark:bg-${getColorClass(config.darkBackgroundColor)}`,
        `dark:text-${getColorClass(config.darkTextColor)}`,
        `dark:border-${getColorClass(config.darkBorderColor)}`,
        `dark:hover:bg-${getColorClass(config.darkHoverBackgroundColor)}`,
        `dark:hover:text-${getColorClass(config.darkHoverTextColor)}`,
        `dark:hover:border-${getColorClass(config.darkHoverBorderColor)}`
      ]);
    }

    const classString = classes.join(' ');
    const attributeName = tailwindFormat === 'className' ? 'className' : 'class';
    
    return `<button ${attributeName}="${classString}">
  ${config.text}
</button>`;
  };

  const copyToClipboard = async (text: string) => {
    try {
      setIsGenerating(true);
      await navigator.clipboard.writeText(text);
      setCopied(true);
      setTimeout(() => {
        setCopied(false);
        setIsGenerating(false);
      }, 2000);
    } catch (err) {
      console.error('Failed to copy:', err);
      setIsGenerating(false);
    }
  };

  const getExportContent = () => {
    switch (exportFormat) {
      case 'css':
        return generateCSS();
      case 'tailwind':
        return generateTailwindCSS();
      default:
        return generateCSS();
    }
  };

  const borderStyles = ['solid', 'dashed', 'dotted', 'double'];
  const fontWeights = [
    { value: '300', label: 'Light' },
    { value: '400', label: 'Normal' },
    { value: '500', label: 'Medium' },
    { value: '600', label: 'Semibold' },
    { value: '700', label: 'Bold' },
  ];

  const shadowPresets = [
    { label: 'None', value: 'none' },
    { label: 'Small', value: '0 1px 2px 0 rgb(0 0 0 / 0.05)' },
    { label: 'Medium', value: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px 0 rgb(0 0 0 / 0.06)' },
    { label: 'Large', value: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -1px rgb(0 0 0 / 0.06)' },
    { label: 'Extra Large', value: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -2px rgb(0 0 0 / 0.05)' },
  ];

  return (
    <div className="p-4 max-w-4xl mx-auto">
      {/* Compact Header */}
      <div className="mb-4">
        <h2 className="text-lg font-semibold text-zinc-900 dark:text-zinc-100 mb-1">
          Button Generator
        </h2>
        <p className="text-sm text-zinc-600 dark:text-zinc-400">
          Create and customize buttons
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {/* Compact Controls */}
        <div className="space-y-4">
          {/* Text */}
          <div className="bg-white dark:bg-zinc-900 rounded-lg border border-zinc-200 dark:border-zinc-800 p-3">
            <h3 className="text-sm font-medium text-zinc-800 dark:text-zinc-200 mb-2">Button Text</h3>
            <input
              type="text"
              value={config.text}
              onChange={(e) => updateConfig('text', e.target.value)}
              className="w-full px-2 py-1 text-sm border border-zinc-300 dark:border-zinc-600 rounded bg-white dark:bg-zinc-800 focus:ring-1 focus:ring-blue-500"
              placeholder="Button label..."
            />
          </div>

          {/* Colors */}
          <div className="bg-white dark:bg-zinc-900 rounded-lg border border-zinc-200 dark:border-zinc-800 p-3">
            <h3 className="text-sm font-medium text-zinc-800 dark:text-zinc-200 mb-2">Colors</h3>
            {/* Light Colors */}
            <div className="mb-3">
              <h4 className="text-xs font-medium text-zinc-600 dark:text-zinc-400 mb-1">Light Mode</h4>
              <div className="grid grid-cols-2 gap-1">
                <ColorInput label="Background" value={config.backgroundColor} onChange={v => updateConfig('backgroundColor', v)} />
                <ColorInput label="Text" value={config.textColor} onChange={v => updateConfig('textColor', v)} />
                <ColorInput label="Border" value={config.borderColor} onChange={v => updateConfig('borderColor', v)} />
                <ColorInput label="Hover BG" value={config.hoverBackgroundColor} onChange={v => updateConfig('hoverBackgroundColor', v)} />
                <ColorInput label="Hover Text" value={config.hoverTextColor} onChange={v => updateConfig('hoverTextColor', v)} />
                <ColorInput label="Hover Border" value={config.hoverBorderColor} onChange={v => updateConfig('hoverBorderColor', v)} />
              </div>
            </div>
            {/* Dark Colors */}
            {config.supportDarkMode && (
              <div>
                <h4 className="text-xs font-medium text-zinc-600 dark:text-zinc-400 mb-1">Dark Mode</h4>
                <div className="grid grid-cols-2 gap-1">
                  <ColorInput label="Background" value={config.darkBackgroundColor} onChange={v => updateConfig('darkBackgroundColor', v)} />
                  <ColorInput label="Text" value={config.darkTextColor} onChange={v => updateConfig('darkTextColor', v)} />
                  <ColorInput label="Border" value={config.darkBorderColor} onChange={v => updateConfig('darkBorderColor', v)} />
                  <ColorInput label="Hover BG" value={config.darkHoverBackgroundColor} onChange={v => updateConfig('darkHoverBackgroundColor', v)} />
                  <ColorInput label="Hover Text" value={config.darkHoverTextColor} onChange={v => updateConfig('darkHoverTextColor', v)} />
                  <ColorInput label="Hover Border" value={config.darkHoverBorderColor} onChange={v => updateConfig('darkHoverBorderColor', v)} />
                </div>
              </div>
            )}
          </div>

          {/* Border & Radius */}
          <div className="bg-white dark:bg-zinc-900 rounded-lg border border-zinc-200 dark:border-zinc-800 p-3">
            <h3 className="text-sm font-medium text-zinc-800 dark:text-zinc-200 mb-2">Border & Radius</h3>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <input type="color" value={config.borderColor} onChange={e => updateConfig('borderColor', e.target.value)} className="w-6 h-6 rounded border border-zinc-300 dark:border-zinc-600" />
                <input type="number" value={config.borderWidth} onChange={e => updateConfig('borderWidth', parseInt(e.target.value))} min="0" max="10" className="w-12 px-1 py-1 text-xs border border-zinc-300 dark:border-zinc-600 rounded bg-white dark:bg-zinc-800" />
                <select value={config.borderStyle} onChange={e => updateConfig('borderStyle', e.target.value)} className="flex-1 px-2 py-1 text-xs border border-zinc-300 dark:border-zinc-600 rounded bg-white dark:bg-zinc-800">
                  {borderStyles.map(style => (<option key={style} value={style}>{style}</option>))}
                </select>
              </div>
              <div>
                <label className="block text-xs text-zinc-600 dark:text-zinc-400 mb-1">Radius: {config.borderRadius}px</label>
                <input type="range" min="0" max="50" value={config.borderRadius} onChange={e => updateConfig('borderRadius', parseInt(e.target.value))} className="w-full h-1" />
              </div>
            </div>
          </div>

          {/* Shadow */}
          <div className="bg-white dark:bg-zinc-900 rounded-lg border border-zinc-200 dark:border-zinc-800 p-3">
            <h3 className="text-sm font-medium text-zinc-800 dark:text-zinc-200 mb-2">Shadow</h3>
            <select value={config.boxShadow} onChange={e => updateConfig('boxShadow', e.target.value)} className="w-full px-2 py-1 text-xs border border-zinc-300 dark:border-zinc-600 rounded bg-white dark:bg-zinc-800">
              {shadowPresets.map(shadow => (<option key={shadow.label} value={shadow.value}>{shadow.label}</option>))}
            </select>
          </div>

          {/* Size & Typography */}
          <div className="bg-white dark:bg-zinc-900 rounded-lg border border-zinc-200 dark:border-zinc-800 p-3">
            <h3 className="text-sm font-medium text-zinc-800 dark:text-zinc-200 mb-2">Size & Typography</h3>
            <div className="space-y-2">
              <div>
                <label className="block text-xs text-zinc-600 dark:text-zinc-400 mb-1">Width: {config.width}px</label>
                <input type="range" min="60" max="400" value={config.width} onChange={e => updateConfig('width', parseInt(e.target.value))} className="w-full h-1" />
              </div>
              <div>
                <label className="block text-xs text-zinc-600 dark:text-zinc-400 mb-1">Height: {config.height}px</label>
                <input type="range" min="24" max="80" value={config.height} onChange={e => updateConfig('height', parseInt(e.target.value))} className="w-full h-1" />
              </div>
              <div>
                <label className="block text-xs text-zinc-600 dark:text-zinc-400 mb-1">Font Size: {config.fontSize}px</label>
                <input type="range" min="10" max="32" value={config.fontSize} onChange={e => updateConfig('fontSize', parseInt(e.target.value))} className="w-full h-1" />
              </div>
              <div>
                <label className="block text-xs text-zinc-600 dark:text-zinc-400 mb-1">Font Weight</label>
                <select value={config.fontWeight} onChange={e => updateConfig('fontWeight', e.target.value)} className="w-full px-2 py-1 text-xs border border-zinc-300 dark:border-zinc-600 rounded bg-white dark:bg-zinc-800">
                  {fontWeights.map(weight => (<option key={weight.value} value={weight.value}>{weight.label}</option>))}
                </select>
              </div>
            </div>
          </div>

          {/* Dark Mode Toggle */}
          <div className="bg-white dark:bg-zinc-900 rounded-lg border border-zinc-200 dark:border-zinc-800 p-3 flex items-center justify-between">
            <span className="text-sm font-medium text-zinc-800 dark:text-zinc-200">Dark Mode Support</span>
            <button
              onClick={() => updateConfig('supportDarkMode', !config.supportDarkMode)}
              className={`relative inline-flex h-5 w-9 items-center rounded-full transition-colors ${config.supportDarkMode ? 'bg-blue-600' : 'bg-zinc-300'}`}
            >
              <span className={`inline-block h-3 w-3 transform rounded-full bg-white shadow transition-transform ${config.supportDarkMode ? 'translate-x-5' : 'translate-x-1'}`} />
            </button>
          </div>
        </div>

        {/* Compact Preview & Export */}
        <div className="space-y-4">
          {/* Preview */}
          <div className="bg-white dark:bg-zinc-900 rounded-lg border border-zinc-200 dark:border-zinc-800 p-3">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-sm font-medium text-zinc-800 dark:text-zinc-200 flex items-center gap-1">
                <Eye className="w-4 h-4" /> Preview
              </h3>
              {config.supportDarkMode && (
                <div className="flex bg-zinc-100 dark:bg-zinc-800 rounded p-0.5">
                  <button
                    onClick={() => setPreviewMode('light')}
                    className={`px-2 py-1 rounded text-xs transition-colors ${previewMode === 'light' ? 'bg-white text-zinc-900 shadow-sm' : 'text-zinc-600 hover:text-zinc-900'}`}
                  >
                    <Sun className="w-3 h-3" />
                  </button>
                  <button
                    onClick={() => setPreviewMode('dark')}
                    className={`px-2 py-1 rounded text-xs transition-colors ${previewMode === 'dark' ? 'bg-white text-zinc-900 shadow-sm' : 'text-zinc-600 hover:text-zinc-900'}`}
                  >
                    <Moon className="w-3 h-3" />
                  </button>
                </div>
              )}
            </div>
            <div className={`flex items-center justify-center h-24 w-full rounded border transition-colors p-3 ${previewMode === 'dark' ? 'bg-zinc-900 border-zinc-700' : 'bg-zinc-50 border-zinc-200'}`}>
              <button
                style={getButtonStyle(false, previewMode === 'dark')}
                onMouseEnter={e => Object.assign(e.currentTarget.style, getButtonStyle(true, previewMode === 'dark'))}
                onMouseLeave={e => Object.assign(e.currentTarget.style, getButtonStyle(false, previewMode === 'dark'))}
                className="transition-all duration-200 max-w-full overflow-hidden"
              >
                {config.text}
              </button>
            </div>
          </div>

          {/* Export */}
          <div className="bg-white dark:bg-zinc-900 rounded-lg border border-zinc-200 dark:border-zinc-800 p-3">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-sm font-medium text-zinc-800 dark:text-zinc-200">Export</h3>
              <div className="flex bg-zinc-100 dark:bg-zinc-800 rounded p-0.5">
                <button
                  onClick={() => setExportFormat('css')}
                  className={`px-2 py-1 rounded text-xs transition-colors ${exportFormat === 'css' ? 'bg-white text-zinc-900 shadow-sm' : 'text-zinc-600 hover:text-zinc-900'}`}
                >CSS</button>
                <button
                  onClick={() => setExportFormat('tailwind')}
                  className={`px-2 py-1 rounded text-xs transition-colors ${exportFormat === 'tailwind' ? 'bg-white text-zinc-900 shadow-sm' : 'text-zinc-600 hover:text-zinc-900'}`}
                >Tailwind</button>
              </div>
            </div>
            {exportFormat === 'tailwind' && (
              <div className="mb-2 p-2 bg-zinc-50 dark:bg-zinc-800 rounded flex gap-3">
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="tailwindFormat"
                    value="className"
                    checked={tailwindFormat === 'className'}
                    onChange={e => setTailwindFormat(e.target.value as 'className' | 'class')}
                    className="mr-1"
                  />
                  <span className="text-xs">className</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="tailwindFormat"
                    value="class"
                    checked={tailwindFormat === 'class'}
                    onChange={e => setTailwindFormat(e.target.value as 'className' | 'class')}
                    className="mr-1"
                  />
                  <span className="text-xs">class</span>
                </label>
              </div>
            )}
            <div className="relative">
              <pre className="bg-zinc-900 text-zinc-100 p-3 rounded overflow-x-auto text-xs max-h-32 overflow-y-auto">
                <code className="whitespace-pre-wrap break-words">{getExportContent()}</code>
              </pre>
              <button
                onClick={() => copyToClipboard(getExportContent())}
                disabled={isGenerating}
                className={`absolute top-2 right-2 p-1 rounded text-xs flex items-center gap-1 transition-colors ${
                  copied
                    ? 'bg-green-600 text-white'
                    : isGenerating
                      ? 'bg-zinc-500 text-white cursor-not-allowed'
                      : 'bg-blue-600 hover:bg-blue-700 text-white'
                }`}
                aria-label="Copy code"
              >
                <Copy className={`w-3 h-3 ${isGenerating ? 'animate-spin' : ''}`} />
                <span className="hidden sm:inline text-xs">
                  {copied ? 'Copied!' : isGenerating ? 'Copying...' : 'Copy'}
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Helper: ColorInput
const ColorInput: React.FC<{ label: string; value: string; onChange: (v: string) => void }> = ({ label, value, onChange }) => (
  <div className="flex items-center gap-1">
    <span className="text-xs text-zinc-600 dark:text-zinc-400 w-12 text-left">{label}</span>
    <input
      type="color"
      value={value}
      onChange={e => onChange(e.target.value)}
      className="w-5 h-5 rounded border border-zinc-300 dark:border-zinc-600 cursor-pointer"
    />
    <input
      type="text"
      value={value}
      onChange={e => onChange(e.target.value)}
      className="flex-1 px-1 py-0.5 text-xs border border-zinc-300 dark:border-zinc-600 rounded font-mono bg-white dark:bg-zinc-800 min-w-0"
      placeholder="#000000"
    />
  </div>
);

export default ButtonGenerator;