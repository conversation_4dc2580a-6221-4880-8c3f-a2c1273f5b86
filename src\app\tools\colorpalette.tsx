'use client';

import React, { useState, useEffect } from 'react';
import { Copy, Download, Shuffle, <PERSON>, EyeOff, Palette, Refresh<PERSON><PERSON>, Heart, Share2, Save, Trash2 } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

const paletteTypes = ['monochromatic', 'analogous', 'complementary', 'triadic', 'tetradic', 'split-complementary', 'all'] as const;
type PaletteType = typeof paletteTypes[number];

interface SavedPalette {
  id: string;
  name: string;
  colors: string[];
  type: PaletteType;
  createdAt: Date;
}

export default function ColorPalette() {
  const [baseColor, setBaseColor] = useState('#3B82F6');
  const [paletteType, setPaletteType] = useState<PaletteType>('monochromatic');
  const [copiedColor, setCopiedColor] = useState<string | null>(null);
  const [showColorValues, setShowColorValues] = useState(true);
  const [colorFormat, setColorFormat] = useState<'hex' | 'rgb' | 'hsl'>('hex');
  const [savedPalettes, setSavedPalettes] = useState<SavedPalette[]>([]);
  const [favoritePalettes, setFavoritePalettes] = useState<string[]>([]);
  const [exportFormat, setExportFormat] = useState<'css' | 'scss' | 'json' | 'tailwind'>('css');
  const [paletteSize, setPaletteSize] = useState(5);

  // Generate color palette based on base color and type
  const generatePalette = (type: PaletteType = paletteType, size: number = paletteSize) => {
    const colors = [];
    const hsl = hexToHsl(baseColor);

    switch (type) {
      case 'monochromatic':
        // Generate shades of the base color
        for (let i = 0; i < size; i++) {
          const lightness = Math.max(10, Math.min(90, 20 + (i * (70 / (size - 1)))));
          colors.push(adjustLightness(baseColor, lightness));
        }
        break;
      case 'analogous':
        // Generate analogous colors (30° apart)
        for (let i = 0; i < size; i++) {
          const hue = (hsl.h + (i - Math.floor(size/2)) * 30 + 360) % 360;
          colors.push(hslToHex(hue, hsl.s, hsl.l));
        }
        break;
      case 'complementary':
        // Generate complementary colors
        colors.push(baseColor);
        colors.push(hslToHex((hsl.h + 180) % 360, hsl.s, hsl.l));
        if (size > 2) {
          colors.push(hslToHex(hsl.h, hsl.s, Math.min(90, hsl.l + 20)));
          colors.push(hslToHex((hsl.h + 180) % 360, hsl.s, Math.min(90, hsl.l + 20)));
        }
        if (size > 4) {
          colors.push(hslToHex(hsl.h, hsl.s, Math.max(10, hsl.l - 20)));
        }
        break;
      case 'triadic':
        // Generate triadic colors (120° apart)
        colors.push(baseColor);
        colors.push(hslToHex((hsl.h + 120) % 360, hsl.s, hsl.l));
        colors.push(hslToHex((hsl.h + 240) % 360, hsl.s, hsl.l));
        if (size > 3) {
          colors.push(hslToHex(hsl.h, hsl.s, Math.min(90, hsl.l + 20)));
          if (size > 4) {
            colors.push(hslToHex(hsl.h, hsl.s, Math.max(10, hsl.l - 20)));
          }
        }
        break;
      case 'tetradic':
        // Generate tetradic colors (90° apart)
        colors.push(baseColor);
        colors.push(hslToHex((hsl.h + 90) % 360, hsl.s, hsl.l));
        colors.push(hslToHex((hsl.h + 180) % 360, hsl.s, hsl.l));
        colors.push(hslToHex((hsl.h + 270) % 360, hsl.s, hsl.l));
        if (size > 4) {
          colors.push(hslToHex(hsl.h, hsl.s, Math.min(90, hsl.l + 20)));
        }
        break;
      case 'split-complementary':
        // Generate split-complementary colors
        colors.push(baseColor);
        colors.push(hslToHex((hsl.h + 150) % 360, hsl.s, hsl.l));
        colors.push(hslToHex((hsl.h + 210) % 360, hsl.s, hsl.l));
        if (size > 3) {
          colors.push(hslToHex(hsl.h, hsl.s, Math.min(90, hsl.l + 20)));
          if (size > 4) {
            colors.push(hslToHex(hsl.h, hsl.s, Math.max(10, hsl.l - 20)));
          }
        }
        break;
    }

    return colors.slice(0, size);
  };

  // For 'all', generate all palettes
  const allPalettes = paletteType === 'all'
    ? {
        monochromatic: generatePalette('monochromatic'),
        analogous: generatePalette('analogous'),
        complementary: generatePalette('complementary'),
        triadic: generatePalette('triadic'),
        tetradic: generatePalette('tetradic'),
        'split-complementary': generatePalette('split-complementary'),
      }
    : null;

  const palette = paletteType !== 'all' ? generatePalette() : [];

  // Color format conversion functions
  const hexToRgb = (hex: string) => {
    const r = parseInt(hex.slice(1, 3), 16);
    const g = parseInt(hex.slice(3, 5), 16);
    const b = parseInt(hex.slice(5, 7), 16);
    return `rgb(${r}, ${g}, ${b})`;
  };

  const formatColor = (color: string, format: 'hex' | 'rgb' | 'hsl') => {
    switch (format) {
      case 'rgb':
        return hexToRgb(color);
      case 'hsl':
        const hsl = hexToHsl(color);
        return `hsl(${Math.round(hsl.h)}, ${Math.round(hsl.s)}%, ${Math.round(hsl.l)}%)`;
      default:
        return color;
    }
  };

  // Random color generation
  const generateRandomColor = () => {
    const hue = Math.floor(Math.random() * 360);
    const saturation = Math.floor(Math.random() * 50) + 50; // 50-100%
    const lightness = Math.floor(Math.random() * 40) + 30; // 30-70%
    return hslToHex(hue, saturation, lightness);
  };

  // Utility functions for color manipulation
  function hexToHsl(hex: string) {
    const r = parseInt(hex.slice(1, 3), 16) / 255;
    const g = parseInt(hex.slice(3, 5), 16) / 255;
    const b = parseInt(hex.slice(5, 7), 16) / 255;

    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);
    let h = 0, s = 0, l = (max + min) / 2;

    if (max !== min) {
      const d = max - min;
      s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
      switch (max) {
        case r: h = (g - b) / d + (g < b ? 6 : 0); break;
        case g: h = (b - r) / d + 2; break;
        case b: h = (r - g) / d + 4; break;
      }
      h /= 6;
    }

    return { h: h * 360, s: s * 100, l: l * 100 };
  }

  function hslToHex(h: number, s: number, l: number) {
    h /= 360;
    s /= 100;
    l /= 100;

    const c = (1 - Math.abs(2 * l - 1)) * s;
    const x = c * (1 - Math.abs((h * 6) % 2 - 1));
    const m = l - c / 2;
    let r = 0, g = 0, b = 0;

    if (0 <= h && h < 1/6) {
      r = c; g = x; b = 0;
    } else if (1/6 <= h && h < 1/3) {
      r = x; g = c; b = 0;
    } else if (1/3 <= h && h < 1/2) {
      r = 0; g = c; b = x;
    } else if (1/2 <= h && h < 2/3) {
      r = 0; g = x; b = c;
    } else if (2/3 <= h && h < 5/6) {
      r = x; g = 0; b = c;
    } else if (5/6 <= h && h <= 1) {
      r = c; g = 0; b = x;
    }

    const rHex = Math.round((r + m) * 255).toString(16).padStart(2, '0');
    const gHex = Math.round((g + m) * 255).toString(16).padStart(2, '0');
    const bHex = Math.round((b + m) * 255).toString(16).padStart(2, '0');

    return `#${rHex}${gHex}${bHex}`.toUpperCase();
  }

  function adjustLightness(hex: string, lightness: number) {
    const hsl = hexToHsl(hex);
    return hslToHex(hsl.h, hsl.s, lightness);
  }

  const copyToClipboard = async (color: string) => {
    try {
      const formattedColor = formatColor(color, colorFormat);
      await navigator.clipboard.writeText(formattedColor);
      setCopiedColor(color);
      setTimeout(() => setCopiedColor(null), 2000);
    } catch (err) {
      console.error('Failed to copy color:', err);
    }
  };

  // Save palette function
  const savePalette = () => {
    const newPalette: SavedPalette = {
      id: Date.now().toString(),
      name: `${paletteType} palette`,
      colors: palette,
      type: paletteType,
      createdAt: new Date(),
    };
    setSavedPalettes(prev => [...prev, newPalette]);
  };

  // Export functions
  const exportPalette = () => {
    const colors = paletteType === 'all' ?
      Object.values(allPalettes || {}).flat() :
      palette;

    let content = '';
    switch (exportFormat) {
      case 'css':
        content = `:root {\n${colors.map((color, i) => `  --color-${i + 1}: ${color};`).join('\n')}\n}`;
        break;
      case 'scss':
        content = colors.map((color, i) => `$color-${i + 1}: ${color};`).join('\n');
        break;
      case 'json':
        content = JSON.stringify(colors, null, 2);
        break;
      case 'tailwind':
        const tailwindColors = colors.reduce((acc, color, i) => {
          acc[`custom-${i + 1}`] = color;
          return acc;
        }, {} as Record<string, string>);
        content = JSON.stringify({ colors: tailwindColors }, null, 2);
        break;
    }

    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `palette.${exportFormat === 'tailwind' ? 'json' : exportFormat}`;
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50 dark:from-zinc-950 dark:via-zinc-900 dark:to-blue-950 p-4 md:p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="p-3 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl shadow-lg">
              <Palette className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
              Color Palette Studio
            </h1>
          </div>
          <p className="text-lg text-zinc-600 dark:text-zinc-400 max-w-2xl mx-auto">
            Create stunning color palettes with advanced color theory algorithms.
            Export in multiple formats and save your favorites.
          </p>
        </motion.div>

        {/* Main Controls */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white/80 dark:bg-zinc-900/80 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 dark:border-zinc-800/50 p-6 md:p-8 mb-8"
        >
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
            {/* Base Color */}
            <div className="space-y-3">
              <label className="block text-sm font-semibold text-zinc-800 dark:text-zinc-200">
                Base Color
              </label>
              <div className="flex items-center gap-3">
                <div className="relative">
                  <input
                    type="color"
                    value={baseColor}
                    onChange={(e) => setBaseColor(e.target.value)}
                    className="w-16 h-16 rounded-2xl border-4 border-white dark:border-zinc-800 shadow-lg cursor-pointer"
                  />
                  <div className="absolute -top-1 -right-1 w-6 h-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                    <div className="w-2 h-2 bg-white rounded-full"></div>
                  </div>
                </div>
                <div className="flex-1">
                  <input
                    type="text"
                    value={baseColor}
                    onChange={(e) => setBaseColor(e.target.value)}
                    className="w-full px-4 py-3 rounded-xl border border-zinc-200 dark:border-zinc-700 text-zinc-800 dark:text-zinc-200 bg-white/50 dark:bg-zinc-800/50 font-mono text-lg backdrop-blur-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                  />
                  <button
                    onClick={() => setBaseColor(generateRandomColor())}
                    className="mt-2 flex items-center gap-2 px-3 py-1.5 text-sm text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-950/50 rounded-lg transition-colors"
                  >
                    <Shuffle className="w-4 h-4" />
                    Random
                  </button>
                </div>
              </div>
            </div>

            {/* Palette Type */}
            <div className="space-y-3">
              <label className="block text-sm font-semibold text-zinc-800 dark:text-zinc-200">
                Palette Type
              </label>
              <select
                value={paletteType}
                onChange={(e) => setPaletteType(e.target.value as PaletteType)}
                className="w-full px-4 py-3 rounded-xl border border-zinc-200 dark:border-zinc-700 text-zinc-800 dark:text-zinc-200 bg-white/50 dark:bg-zinc-800/50 backdrop-blur-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
              >
                <option value="monochromatic">Monochromatic</option>
                <option value="analogous">Analogous</option>
                <option value="complementary">Complementary</option>
                <option value="triadic">Triadic</option>
                <option value="tetradic">Tetradic</option>
                <option value="split-complementary">Split Complementary</option>
                <option value="all">All Types</option>
              </select>
            </div>

            {/* Palette Size */}
            <div className="space-y-3">
              <label className="block text-sm font-semibold text-zinc-800 dark:text-zinc-200">
                Colors ({paletteSize})
              </label>
              <input
                type="range"
                min="3"
                max="8"
                value={paletteSize}
                onChange={(e) => setPaletteSize(Number(e.target.value))}
                className="w-full h-3 bg-zinc-200 dark:bg-zinc-700 rounded-lg appearance-none cursor-pointer slider"
              />
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-wrap gap-3 justify-center">
            <button
              onClick={() => setShowColorValues(!showColorValues)}
              className="flex items-center gap-2 px-4 py-2 bg-zinc-100 dark:bg-zinc-800 hover:bg-zinc-200 dark:hover:bg-zinc-700 rounded-xl transition-colors"
            >
              {showColorValues ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              {showColorValues ? 'Hide Values' : 'Show Values'}
            </button>
            <button
              onClick={savePalette}
              className="flex items-center gap-2 px-4 py-2 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 hover:bg-green-200 dark:hover:bg-green-900/50 rounded-xl transition-colors"
            >
              <Save className="w-4 h-4" />
              Save Palette
            </button>
            <button
              onClick={exportPalette}
              className="flex items-center gap-2 px-4 py-2 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 hover:bg-blue-200 dark:hover:bg-blue-900/50 rounded-xl transition-colors"
            >
              <Download className="w-4 h-4" />
              Export
            </button>
          </div>
        </motion.div>

        {/* Generated Palette */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="mb-8"
        >
          {paletteType !== 'all' ? (
            <div className="bg-white/80 dark:bg-zinc-900/80 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 dark:border-zinc-800/50 p-6 md:p-8">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-2xl font-bold text-zinc-800 dark:text-zinc-200 capitalize">
                  {paletteType.replace('-', ' ')} Palette
                </h3>
                <div className="flex items-center gap-2">
                  <select
                    value={colorFormat}
                    onChange={(e) => setColorFormat(e.target.value as 'hex' | 'rgb' | 'hsl')}
                    className="px-3 py-1.5 text-sm rounded-lg border border-zinc-200 dark:border-zinc-700 bg-white/50 dark:bg-zinc-800/50"
                  >
                    <option value="hex">HEX</option>
                    <option value="rgb">RGB</option>
                    <option value="hsl">HSL</option>
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
                {palette.map((color, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: index * 0.1 }}
                    className="group relative"
                  >
                    <div
                      className="w-full aspect-square rounded-2xl shadow-lg cursor-pointer transform transition-all duration-300 group-hover:scale-105 group-hover:shadow-2xl border-4 border-white dark:border-zinc-800"
                      style={{ backgroundColor: color }}
                      onClick={() => copyToClipboard(color)}
                    >
                      <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 rounded-2xl transition-colors duration-300 flex items-center justify-center">
                        <motion.div
                          initial={{ opacity: 0, scale: 0.5 }}
                          whileHover={{ opacity: 1, scale: 1 }}
                          className="bg-white/90 dark:bg-zinc-900/90 backdrop-blur-sm rounded-full p-2 shadow-lg"
                        >
                          <Copy className="w-5 h-5 text-zinc-700 dark:text-zinc-300" />
                        </motion.div>
                      </div>
                    </div>

                    {showColorValues && (
                      <div className="mt-3 text-center">
                        <div className="text-sm font-mono text-zinc-800 dark:text-zinc-200 font-semibold">
                          {formatColor(color, colorFormat)}
                        </div>
                        <AnimatePresence>
                          {copiedColor === color && (
                            <motion.div
                              initial={{ opacity: 0, y: 10 }}
                              animate={{ opacity: 1, y: 0 }}
                              exit={{ opacity: 0, y: -10 }}
                              className="text-xs text-green-600 dark:text-green-400 font-medium"
                            >
                              Copied!
                            </motion.div>
                          )}
                        </AnimatePresence>
                      </div>
                    )}
                  </motion.div>
                ))}
              </div>
            </div>
          ) : (
            <div className="space-y-8">
              {allPalettes && Object.entries(allPalettes).map(([type, colors], paletteIndex) => (
                <motion.div
                  key={type}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: paletteIndex * 0.1 }}
                  className="bg-white/80 dark:bg-zinc-900/80 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 dark:border-zinc-800/50 p-6 md:p-8"
                >
                  <h4 className="text-xl font-bold capitalize text-zinc-700 dark:text-zinc-300 mb-6">
                    {type.replace('-', ' ')} Palette
                  </h4>
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
                    {colors.map((color, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: index * 0.05 }}
                        className="group relative"
                      >
                        <div
                          className="w-full aspect-square rounded-2xl shadow-lg cursor-pointer transform transition-all duration-300 group-hover:scale-105 group-hover:shadow-2xl border-4 border-white dark:border-zinc-800"
                          style={{ backgroundColor: color }}
                          onClick={() => copyToClipboard(color)}
                        >
                          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 rounded-2xl transition-colors duration-300 flex items-center justify-center">
                            <motion.div
                              initial={{ opacity: 0, scale: 0.5 }}
                              whileHover={{ opacity: 1, scale: 1 }}
                              className="bg-white/90 dark:bg-zinc-900/90 backdrop-blur-sm rounded-full p-2 shadow-lg"
                            >
                              <Copy className="w-5 h-5 text-zinc-700 dark:text-zinc-300" />
                            </motion.div>
                          </div>
                        </div>

                        {showColorValues && (
                          <div className="mt-3 text-center">
                            <div className="text-sm font-mono text-zinc-800 dark:text-zinc-200 font-semibold">
                              {formatColor(color, colorFormat)}
                            </div>
                            <AnimatePresence>
                              {copiedColor === color && (
                                <motion.div
                                  initial={{ opacity: 0, y: 10 }}
                                  animate={{ opacity: 1, y: 0 }}
                                  exit={{ opacity: 0, y: -10 }}
                                  className="text-xs text-green-600 dark:text-green-400 font-medium"
                                >
                                  Copied!
                                </motion.div>
                              )}
                            </AnimatePresence>
                          </div>
                        )}
                      </motion.div>
                    ))}
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </motion.div>

        {/* Export Options & CSS Output */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white/80 dark:bg-zinc-900/80 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 dark:border-zinc-800/50 p-6 md:p-8"
        >
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-2xl font-bold text-zinc-800 dark:text-zinc-200">
              Export & Code
            </h3>
            <div className="flex items-center gap-2">
              <select
                value={exportFormat}
                onChange={(e) => setExportFormat(e.target.value as 'css' | 'scss' | 'json' | 'tailwind')}
                className="px-3 py-1.5 text-sm rounded-lg border border-zinc-200 dark:border-zinc-700 bg-white/50 dark:bg-zinc-800/50"
              >
                <option value="css">CSS</option>
                <option value="scss">SCSS</option>
                <option value="json">JSON</option>
                <option value="tailwind">Tailwind</option>
              </select>
            </div>
          </div>

          <div className="bg-zinc-50/50 dark:bg-zinc-800/50 rounded-2xl p-6 border border-zinc-200/50 dark:border-zinc-700/50">
            <div className="flex items-center justify-between mb-4">
              <div className="text-sm font-semibold text-zinc-600 dark:text-zinc-400">
                {exportFormat.toUpperCase()} Output
              </div>
              <button
                onClick={() => {
                  const colors = paletteType === 'all' ?
                    Object.values(allPalettes || {}).flat() :
                    palette;

                  let content = '';
                  switch (exportFormat) {
                    case 'css':
                      content = `:root {\n${colors.map((color, i) => `  --color-${i + 1}: ${color};`).join('\n')}\n}`;
                      break;
                    case 'scss':
                      content = colors.map((color, i) => `$color-${i + 1}: ${color};`).join('\n');
                      break;
                    case 'json':
                      content = JSON.stringify(colors, null, 2);
                      break;
                    case 'tailwind':
                      const tailwindColors = colors.reduce((acc, color, i) => {
                        acc[`custom-${i + 1}`] = color;
                        return acc;
                      }, {} as Record<string, string>);
                      content = JSON.stringify({ colors: tailwindColors }, null, 2);
                      break;
                  }
                  navigator.clipboard.writeText(content);
                }}
                className="flex items-center gap-2 px-3 py-1.5 text-sm bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 hover:bg-blue-200 dark:hover:bg-blue-900/50 rounded-lg transition-colors"
              >
                <Copy className="w-4 h-4" />
                Copy
              </button>
            </div>

            <pre className="text-sm text-zinc-800 dark:text-zinc-200 font-mono overflow-x-auto">
              <code>
                {(() => {
                  const colors = paletteType === 'all' ?
                    Object.values(allPalettes || {}).flat() :
                    palette;

                  switch (exportFormat) {
                    case 'css':
                      return `:root {\n${colors.map((color, i) => `  --color-${i + 1}: ${color};`).join('\n')}\n}`;
                    case 'scss':
                      return colors.map((color, i) => `$color-${i + 1}: ${color};`).join('\n');
                    case 'json':
                      return JSON.stringify(colors, null, 2);
                    case 'tailwind':
                      const tailwindColors = colors.reduce((acc, color, i) => {
                        acc[`custom-${i + 1}`] = color;
                        return acc;
                      }, {} as Record<string, string>);
                      return JSON.stringify({ colors: tailwindColors }, null, 2);
                    default:
                      return '';
                  }
                })()}
              </code>
            </pre>
          </div>
        </motion.div>
      </div>
    </div>
  );
}