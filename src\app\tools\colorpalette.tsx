'use client';

import React, { useState } from 'react';
import { Copy, Download, Shuffle, Eye, EyeOff, Save } from 'lucide-react';

const paletteTypes = ['monochromatic', 'analogous', 'complementary', 'triadic', 'tetradic', 'split-complementary', 'all'] as const;
type PaletteType = typeof paletteTypes[number];

interface SavedPalette {
  id: string;
  name: string;
  colors: string[];
  type: PaletteType;
  createdAt: Date;
}

export default function ColorPalette() {
  const [baseColor, setBaseColor] = useState('#3B82F6');
  const [paletteType, setPaletteType] = useState<PaletteType>('monochromatic');
  const [copiedColor, setCopiedColor] = useState<string | null>(null);
  const [showColorValues, setShowColorValues] = useState(true);
  const [colorFormat, setColorFormat] = useState<'hex' | 'rgb' | 'hsl'>('hex');
  const [savedPalettes, setSavedPalettes] = useState<SavedPalette[]>([]);
  const [favoritePalettes, setFavoritePalettes] = useState<string[]>([]);
  const [exportFormat, setExportFormat] = useState<'css' | 'scss' | 'json' | 'tailwind'>('css');
  const [paletteSize, setPaletteSize] = useState(5);

  // Generate color palette based on base color and type
  const generatePalette = (type: PaletteType = paletteType, size: number = paletteSize) => {
    const colors = [];
    const hsl = hexToHsl(baseColor);

    switch (type) {
      case 'monochromatic':
        // Generate shades of the base color
        for (let i = 0; i < size; i++) {
          const lightness = Math.max(10, Math.min(90, 20 + (i * (70 / (size - 1)))));
          colors.push(adjustLightness(baseColor, lightness));
        }
        break;
      case 'analogous':
        // Generate analogous colors (30° apart)
        for (let i = 0; i < size; i++) {
          const hue = (hsl.h + (i - Math.floor(size/2)) * 30 + 360) % 360;
          colors.push(hslToHex(hue, hsl.s, hsl.l));
        }
        break;
      case 'complementary':
        // Generate complementary colors
        colors.push(baseColor);
        colors.push(hslToHex((hsl.h + 180) % 360, hsl.s, hsl.l));
        if (size > 2) {
          colors.push(hslToHex(hsl.h, hsl.s, Math.min(90, hsl.l + 20)));
          colors.push(hslToHex((hsl.h + 180) % 360, hsl.s, Math.min(90, hsl.l + 20)));
        }
        if (size > 4) {
          colors.push(hslToHex(hsl.h, hsl.s, Math.max(10, hsl.l - 20)));
        }
        break;
      case 'triadic':
        // Generate triadic colors (120° apart)
        colors.push(baseColor);
        colors.push(hslToHex((hsl.h + 120) % 360, hsl.s, hsl.l));
        colors.push(hslToHex((hsl.h + 240) % 360, hsl.s, hsl.l));
        if (size > 3) {
          colors.push(hslToHex(hsl.h, hsl.s, Math.min(90, hsl.l + 20)));
          if (size > 4) {
            colors.push(hslToHex(hsl.h, hsl.s, Math.max(10, hsl.l - 20)));
          }
        }
        break;
      case 'tetradic':
        // Generate tetradic colors (90° apart)
        colors.push(baseColor);
        colors.push(hslToHex((hsl.h + 90) % 360, hsl.s, hsl.l));
        colors.push(hslToHex((hsl.h + 180) % 360, hsl.s, hsl.l));
        colors.push(hslToHex((hsl.h + 270) % 360, hsl.s, hsl.l));
        if (size > 4) {
          colors.push(hslToHex(hsl.h, hsl.s, Math.min(90, hsl.l + 20)));
        }
        break;
      case 'split-complementary':
        // Generate split-complementary colors
        colors.push(baseColor);
        colors.push(hslToHex((hsl.h + 150) % 360, hsl.s, hsl.l));
        colors.push(hslToHex((hsl.h + 210) % 360, hsl.s, hsl.l));
        if (size > 3) {
          colors.push(hslToHex(hsl.h, hsl.s, Math.min(90, hsl.l + 20)));
          if (size > 4) {
            colors.push(hslToHex(hsl.h, hsl.s, Math.max(10, hsl.l - 20)));
          }
        }
        break;
    }

    return colors.slice(0, size);
  };

  // For 'all', generate all palettes
  const allPalettes = paletteType === 'all'
    ? {
        monochromatic: generatePalette('monochromatic'),
        analogous: generatePalette('analogous'),
        complementary: generatePalette('complementary'),
        triadic: generatePalette('triadic'),
        tetradic: generatePalette('tetradic'),
        'split-complementary': generatePalette('split-complementary'),
      }
    : null;

  const palette = paletteType !== 'all' ? generatePalette() : [];

  // Color format conversion functions
  const hexToRgb = (hex: string) => {
    const r = parseInt(hex.slice(1, 3), 16);
    const g = parseInt(hex.slice(3, 5), 16);
    const b = parseInt(hex.slice(5, 7), 16);
    return `rgb(${r}, ${g}, ${b})`;
  };

  const formatColor = (color: string, format: 'hex' | 'rgb' | 'hsl') => {
    switch (format) {
      case 'rgb':
        return hexToRgb(color);
      case 'hsl':
        const hsl = hexToHsl(color);
        return `hsl(${Math.round(hsl.h)}, ${Math.round(hsl.s)}%, ${Math.round(hsl.l)}%)`;
      default:
        return color;
    }
  };

  // Random color generation
  const generateRandomColor = () => {
    const hue = Math.floor(Math.random() * 360);
    const saturation = Math.floor(Math.random() * 50) + 50; // 50-100%
    const lightness = Math.floor(Math.random() * 40) + 30; // 30-70%
    return hslToHex(hue, saturation, lightness);
  };

  // Utility functions for color manipulation
  function hexToHsl(hex: string) {
    const r = parseInt(hex.slice(1, 3), 16) / 255;
    const g = parseInt(hex.slice(3, 5), 16) / 255;
    const b = parseInt(hex.slice(5, 7), 16) / 255;

    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);
    let h = 0, s = 0, l = (max + min) / 2;

    if (max !== min) {
      const d = max - min;
      s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
      switch (max) {
        case r: h = (g - b) / d + (g < b ? 6 : 0); break;
        case g: h = (b - r) / d + 2; break;
        case b: h = (r - g) / d + 4; break;
      }
      h /= 6;
    }

    return { h: h * 360, s: s * 100, l: l * 100 };
  }

  function hslToHex(h: number, s: number, l: number) {
    h /= 360;
    s /= 100;
    l /= 100;

    const c = (1 - Math.abs(2 * l - 1)) * s;
    const x = c * (1 - Math.abs((h * 6) % 2 - 1));
    const m = l - c / 2;
    let r = 0, g = 0, b = 0;

    if (0 <= h && h < 1/6) {
      r = c; g = x; b = 0;
    } else if (1/6 <= h && h < 1/3) {
      r = x; g = c; b = 0;
    } else if (1/3 <= h && h < 1/2) {
      r = 0; g = c; b = x;
    } else if (1/2 <= h && h < 2/3) {
      r = 0; g = x; b = c;
    } else if (2/3 <= h && h < 5/6) {
      r = x; g = 0; b = c;
    } else if (5/6 <= h && h <= 1) {
      r = c; g = 0; b = x;
    }

    const rHex = Math.round((r + m) * 255).toString(16).padStart(2, '0');
    const gHex = Math.round((g + m) * 255).toString(16).padStart(2, '0');
    const bHex = Math.round((b + m) * 255).toString(16).padStart(2, '0');

    return `#${rHex}${gHex}${bHex}`.toUpperCase();
  }

  function adjustLightness(hex: string, lightness: number) {
    const hsl = hexToHsl(hex);
    return hslToHex(hsl.h, hsl.s, lightness);
  }

  const copyToClipboard = async (color: string) => {
    try {
      const formattedColor = formatColor(color, colorFormat);
      await navigator.clipboard.writeText(formattedColor);
      setCopiedColor(color);
      setTimeout(() => setCopiedColor(null), 2000);
    } catch (err) {
      console.error('Failed to copy color:', err);
    }
  };

  // Save palette function
  const savePalette = () => {
    const newPalette: SavedPalette = {
      id: Date.now().toString(),
      name: `${paletteType} palette`,
      colors: palette,
      type: paletteType,
      createdAt: new Date(),
    };
    setSavedPalettes(prev => [...prev, newPalette]);
  };

  // Export functions
  const exportPalette = () => {
    const colors = paletteType === 'all' ?
      Object.values(allPalettes || {}).flat() :
      palette;

    let content = '';
    switch (exportFormat) {
      case 'css':
        content = `:root {\n${colors.map((color, i) => `  --color-${i + 1}: ${color};`).join('\n')}\n}`;
        break;
      case 'scss':
        content = colors.map((color, i) => `$color-${i + 1}: ${color};`).join('\n');
        break;
      case 'json':
        content = JSON.stringify(colors, null, 2);
        break;
      case 'tailwind':
        const tailwindColors = colors.reduce((acc, color, i) => {
          acc[`custom-${i + 1}`] = color;
          return acc;
        }, {} as Record<string, string>);
        content = JSON.stringify({ colors: tailwindColors }, null, 2);
        break;
    }

    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `palette.${exportFormat === 'tailwind' ? 'json' : exportFormat}`;
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className="p-4 max-w-4xl mx-auto">
      {/* Compact Header */}
      <div className="mb-4">
        <h2 className="text-lg font-semibold text-zinc-900 dark:text-zinc-100 mb-1">
          Color Palette
        </h2>
        <p className="text-sm text-zinc-600 dark:text-zinc-400">
          Generate color palettes from a base color
        </p>
      </div>

      {/* Compact Controls */}
      <div className="bg-white dark:bg-zinc-900 rounded-lg border border-zinc-200 dark:border-zinc-800 p-4 mb-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mb-3">
          {/* Base Color */}
          <div>
            <label className="block text-xs font-medium text-zinc-700 dark:text-zinc-300 mb-1">
              Base Color
            </label>
            <div className="flex items-center gap-2">
              <input
                type="color"
                value={baseColor}
                onChange={(e) => setBaseColor(e.target.value)}
                className="w-8 h-8 rounded border border-zinc-300 dark:border-zinc-600 cursor-pointer"
              />
              <input
                type="text"
                value={baseColor}
                onChange={(e) => setBaseColor(e.target.value)}
                className="flex-1 px-2 py-1 text-sm rounded border border-zinc-300 dark:border-zinc-600 bg-white dark:bg-zinc-800 font-mono"
              />
              <button
                onClick={() => setBaseColor(generateRandomColor())}
                className="p-1 text-zinc-500 hover:text-zinc-700 dark:hover:text-zinc-300"
                title="Random color"
              >
                <Shuffle className="w-4 h-4" />
              </button>
            </div>
          </div>

          {/* Palette Type */}
          <div>
            <label className="block text-xs font-medium text-zinc-700 dark:text-zinc-300 mb-1">
              Type
            </label>
            <select
              value={paletteType}
              onChange={(e) => setPaletteType(e.target.value as PaletteType)}
              className="w-full px-2 py-1 text-sm rounded border border-zinc-300 dark:border-zinc-600 bg-white dark:bg-zinc-800"
            >
              <option value="monochromatic">Monochromatic</option>
              <option value="analogous">Analogous</option>
              <option value="complementary">Complementary</option>
              <option value="triadic">Triadic</option>
              <option value="tetradic">Tetradic</option>
              <option value="split-complementary">Split Complementary</option>
              <option value="all">All Types</option>
            </select>
          </div>

          {/* Palette Size */}
          <div>
            <label className="block text-xs font-medium text-zinc-700 dark:text-zinc-300 mb-1">
              Colors ({paletteSize})
            </label>
            <input
              type="range"
              min="3"
              max="8"
              value={paletteSize}
              onChange={(e) => setPaletteSize(Number(e.target.value))}
              className="w-full h-2 bg-zinc-200 dark:bg-zinc-700 rounded appearance-none cursor-pointer"
            />
          </div>
        </div>

        {/* Compact Action Buttons */}
        <div className="flex gap-2 text-xs">
          <button
            onClick={() => setShowColorValues(!showColorValues)}
            className="flex items-center gap-1 px-2 py-1 bg-zinc-100 dark:bg-zinc-800 hover:bg-zinc-200 dark:hover:bg-zinc-700 rounded transition-colors"
          >
            {showColorValues ? <EyeOff className="w-3 h-3" /> : <Eye className="w-3 h-3" />}
            {showColorValues ? 'Hide' : 'Show'}
          </button>
          <button
            onClick={savePalette}
            className="flex items-center gap-1 px-2 py-1 bg-zinc-100 dark:bg-zinc-800 hover:bg-zinc-200 dark:hover:bg-zinc-700 rounded transition-colors"
          >
            <Save className="w-3 h-3" />
            Save
          </button>
          <button
            onClick={exportPalette}
            className="flex items-center gap-1 px-2 py-1 bg-zinc-100 dark:bg-zinc-800 hover:bg-zinc-200 dark:hover:bg-zinc-700 rounded transition-colors"
          >
            <Download className="w-3 h-3" />
            Export
          </button>
        </div>
      </div>

      {/* Compact Palette Display */}
      <div className="mb-4">
        {paletteType !== 'all' ? (
          <div className="bg-white dark:bg-zinc-900 rounded-lg border border-zinc-200 dark:border-zinc-800 p-3">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-sm font-medium text-zinc-800 dark:text-zinc-200 capitalize">
                {paletteType.replace('-', ' ')}
              </h3>
              <select
                value={colorFormat}
                onChange={(e) => setColorFormat(e.target.value as 'hex' | 'rgb' | 'hsl')}
                className="px-2 py-1 text-xs rounded border border-zinc-300 dark:border-zinc-600 bg-white dark:bg-zinc-800"
              >
                <option value="hex">HEX</option>
                <option value="rgb">RGB</option>
                <option value="hsl">HSL</option>
              </select>
            </div>

            <div className="grid grid-cols-5 gap-2">
              {palette.map((color, index) => (
                <div key={index} className="group relative">
                  <div
                    className="w-full h-16 rounded cursor-pointer border border-zinc-200 dark:border-zinc-700 group-hover:scale-105 transition-transform"
                    style={{ backgroundColor: color }}
                    onClick={() => copyToClipboard(color)}
                    title={`Click to copy ${formatColor(color, colorFormat)}`}
                  >
                    <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 rounded transition-colors flex items-center justify-center">
                      <Copy className="w-3 h-3 text-white opacity-0 group-hover:opacity-100 transition-opacity drop-shadow-lg" />
                    </div>
                  </div>

                  {showColorValues && (
                    <div className="mt-1 text-center">
                      <div className="text-xs font-mono text-zinc-700 dark:text-zinc-300">
                        {formatColor(color, colorFormat)}
                      </div>
                      {copiedColor === color && (
                        <div className="text-xs text-green-600 dark:text-green-400">
                          Copied!
                        </div>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        ) : (
          <div className="space-y-3">
            {allPalettes && Object.entries(allPalettes).map(([type, colors]) => (
              <div key={type} className="bg-white dark:bg-zinc-900 rounded-lg border border-zinc-200 dark:border-zinc-800 p-3">
                <h4 className="text-sm font-medium capitalize text-zinc-700 dark:text-zinc-300 mb-2">
                  {type.replace('-', ' ')}
                </h4>
                <div className="grid grid-cols-5 gap-2">
                  {colors.map((color, index) => (
                    <div key={index} className="group relative">
                      <div
                        className="w-full h-12 rounded cursor-pointer border border-zinc-200 dark:border-zinc-700 group-hover:scale-105 transition-transform"
                        style={{ backgroundColor: color }}
                        onClick={() => copyToClipboard(color)}
                        title={`Click to copy ${formatColor(color, colorFormat)}`}
                      >
                        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 rounded transition-colors flex items-center justify-center">
                          <Copy className="w-3 h-3 text-white opacity-0 group-hover:opacity-100 transition-opacity drop-shadow-lg" />
                        </div>
                      </div>

                      {showColorValues && (
                        <div className="mt-1 text-center">
                          <div className="text-xs font-mono text-zinc-700 dark:text-zinc-300">
                            {formatColor(color, colorFormat)}
                          </div>
                          {copiedColor === color && (
                            <div className="text-xs text-green-600 dark:text-green-400">
                              Copied!
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Compact Export Section */}
      <div className="bg-white dark:bg-zinc-900 rounded-lg border border-zinc-200 dark:border-zinc-800 p-3">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-sm font-medium text-zinc-800 dark:text-zinc-200">
            Export Code
          </h3>
          <div className="flex items-center gap-2">
            <select
              value={exportFormat}
              onChange={(e) => setExportFormat(e.target.value as 'css' | 'scss' | 'json' | 'tailwind')}
              className="px-2 py-1 text-xs rounded border border-zinc-300 dark:border-zinc-600 bg-white dark:bg-zinc-800"
            >
              <option value="css">CSS</option>
              <option value="scss">SCSS</option>
              <option value="json">JSON</option>
              <option value="tailwind">Tailwind</option>
            </select>
            <button
              onClick={() => {
                const colors = paletteType === 'all' ?
                  Object.values(allPalettes || {}).flat() :
                  palette;

                let content = '';
                switch (exportFormat) {
                  case 'css':
                    content = `:root {\n${colors.map((color, i) => `  --color-${i + 1}: ${color};`).join('\n')}\n}`;
                    break;
                  case 'scss':
                    content = colors.map((color, i) => `$color-${i + 1}: ${color};`).join('\n');
                    break;
                  case 'json':
                    content = JSON.stringify(colors, null, 2);
                    break;
                  case 'tailwind':
                    const tailwindColors = colors.reduce((acc, color, i) => {
                      acc[`custom-${i + 1}`] = color;
                      return acc;
                    }, {} as Record<string, string>);
                    content = JSON.stringify({ colors: tailwindColors }, null, 2);
                    break;
                }
                navigator.clipboard.writeText(content);
              }}
              className="flex items-center gap-1 px-2 py-1 text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 hover:bg-blue-200 dark:hover:bg-blue-900/50 rounded transition-colors"
            >
              <Copy className="w-3 h-3" />
              Copy
            </button>
          </div>
        </div>

        <div className="bg-zinc-50 dark:bg-zinc-800 rounded p-2 border border-zinc-200 dark:border-zinc-700">
          <pre className="text-xs text-zinc-800 dark:text-zinc-200 font-mono overflow-x-auto max-h-32 overflow-y-auto">
            <code>
              {(() => {
                const colors = paletteType === 'all' ?
                  Object.values(allPalettes || {}).flat() :
                  palette;

                switch (exportFormat) {
                  case 'css':
                    return `:root {\n${colors.map((color, i) => `  --color-${i + 1}: ${color};`).join('\n')}\n}`;
                  case 'scss':
                    return colors.map((color, i) => `$color-${i + 1}: ${color};`).join('\n');
                  case 'json':
                    return JSON.stringify(colors, null, 2);
                  case 'tailwind':
                    const tailwindColors = colors.reduce((acc, color, i) => {
                      acc[`custom-${i + 1}`] = color;
                      return acc;
                    }, {} as Record<string, string>);
                    return JSON.stringify({ colors: tailwindColors }, null, 2);
                  default:
                    return '';
                }
              })()}
            </code>
          </pre>
        </div>
      </div>
    </div>
  );
}