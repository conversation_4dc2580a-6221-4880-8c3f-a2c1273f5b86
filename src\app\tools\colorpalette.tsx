'use client';

import React, { useState } from 'react';

const paletteTypes = ['monochromatic', 'analogous', 'complementary', 'triadic', 'all'] as const;
type PaletteType = typeof paletteTypes[number];

export default function ColorPalette() {
  const [baseColor, setBaseColor] = useState('#3B82F6');
  const [paletteType, setPaletteType] = useState<PaletteType>('monochromatic');

  // Generate color palette based on base color and type
  const generatePalette = (type: PaletteType = paletteType) => {
    const colors = [];
    switch (type) {
      case 'monochromatic':
        // Generate 5 shades of the base color
        for (let i = 0; i < 5; i++) {
          const lightness = 20 + (i * 15);
          colors.push(adjustLightness(baseColor, lightness));
        }
        break;
      case 'analogous':
        // Generate 5 analogous colors (30° apart)
        for (let i = 0; i < 5; i++) {
          const hue = (hexToHsl(baseColor).h + (i - 2) * 30 + 360) % 360;
          colors.push(hslToHex(hue, 70, 50));
        }
        break;
      case 'complementary':
        // Generate complementary colors
        const hsl = hexToHsl(baseColor);
        colors.push(baseColor);
        colors.push(hslToHex((hsl.h + 180) % 360, hsl.s, hsl.l));
        colors.push(hslToHex((hsl.h + 90) % 360, hsl.s, hsl.l));
        colors.push(hslToHex((hsl.h + 270) % 360, hsl.s, hsl.l));
        colors.push(hslToHex(hsl.h, hsl.s, 80));
        break;
      case 'triadic':
        // Generate triadic colors (120° apart)
        const baseHsl = hexToHsl(baseColor);
        colors.push(baseColor);
        colors.push(hslToHex((baseHsl.h + 120) % 360, baseHsl.s, baseHsl.l));
        colors.push(hslToHex((baseHsl.h + 240) % 360, baseHsl.s, baseHsl.l));
        colors.push(hslToHex(baseHsl.h, baseHsl.s, 80));
        colors.push(hslToHex(baseHsl.h, baseHsl.s, 20));
        break;
    }
    
    return colors;
  };

  // For 'all', generate all palettes
  const allPalettes = paletteType === 'all'
    ? {
        monochromatic: generatePalette('monochromatic'),
        analogous: generatePalette('analogous'),
        complementary: generatePalette('complementary'),
        triadic: generatePalette('triadic'),
      }
    : null;

  const palette = paletteType !== 'all' ? generatePalette() : [];

  // Utility functions for color manipulation
  function hexToHsl(hex: string) {
    const r = parseInt(hex.slice(1, 3), 16) / 255;
    const g = parseInt(hex.slice(3, 5), 16) / 255;
    const b = parseInt(hex.slice(5, 7), 16) / 255;

    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);
    let h = 0, s = 0, l = (max + min) / 2;

    if (max !== min) {
      const d = max - min;
      s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
      switch (max) {
        case r: h = (g - b) / d + (g < b ? 6 : 0); break;
        case g: h = (b - r) / d + 2; break;
        case b: h = (r - g) / d + 4; break;
      }
      h /= 6;
    }

    return { h: h * 360, s: s * 100, l: l * 100 };
  }

  function hslToHex(h: number, s: number, l: number) {
    h /= 360;
    s /= 100;
    l /= 100;

    const c = (1 - Math.abs(2 * l - 1)) * s;
    const x = c * (1 - Math.abs((h * 6) % 2 - 1));
    const m = l - c / 2;
    let r = 0, g = 0, b = 0;

    if (0 <= h && h < 1/6) {
      r = c; g = x; b = 0;
    } else if (1/6 <= h && h < 1/3) {
      r = x; g = c; b = 0;
    } else if (1/3 <= h && h < 1/2) {
      r = 0; g = c; b = x;
    } else if (1/2 <= h && h < 2/3) {
      r = 0; g = x; b = c;
    } else if (2/3 <= h && h < 5/6) {
      r = x; g = 0; b = c;
    } else if (5/6 <= h && h <= 1) {
      r = c; g = 0; b = x;
    }

    const rHex = Math.round((r + m) * 255).toString(16).padStart(2, '0');
    const gHex = Math.round((g + m) * 255).toString(16).padStart(2, '0');
    const bHex = Math.round((b + m) * 255).toString(16).padStart(2, '0');

    return `#${rHex}${gHex}${bHex}`.toUpperCase();
  }

  function adjustLightness(hex: string, lightness: number) {
    const hsl = hexToHsl(hex);
    return hslToHex(hsl.h, hsl.s, lightness);
  }

  const copyToClipboard = (color: string) => {
    navigator.clipboard.writeText(color);
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-zinc-900 dark:text-zinc-100 mb-4">
          Color Palette Generator
        </h2>
        <p className="text-zinc-600 dark:text-zinc-400 mb-6">
          Generate beautiful color palettes from a base color
        </p>
      </div>

      {/* Controls */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <div>
          <label className="block text-sm font-medium text-zinc-800 dark:text-zinc-200 mb-2">
            Base Color
          </label>
          <div className="flex items-center gap-3">
            <input
              type="color"
              value={baseColor}
              onChange={(e) => setBaseColor(e.target.value)}
              className="w-16 h-12 rounded border border-zinc-200 dark:border-zinc-700"
            />
            <input
              type="text"
              value={baseColor}
              onChange={(e) => setBaseColor(e.target.value)}
              className="flex-1 px-3 py-2 rounded border border-zinc-200 dark:border-zinc-700 text-zinc-800 dark:text-zinc-200 bg-white dark:bg-zinc-800 font-mono"
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-zinc-800 dark:text-zinc-200 mb-2">
            Palette Type
          </label>
          <select
            value={paletteType}
            onChange={(e) => setPaletteType(e.target.value as PaletteType)}
            className="w-full px-3 py-2 rounded border border-zinc-200 dark:border-zinc-700 text-zinc-800 dark:text-zinc-200 bg-white dark:bg-zinc-800"
          >
            <option value="monochromatic">Monochromatic</option>
            <option value="analogous">Analogous</option>
            <option value="complementary">Complementary</option>
            <option value="triadic">Triadic</option>
            <option value="all">All</option>
          </select>
        </div>
      </div>

      {/* Generated Palette */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-zinc-800 dark:text-zinc-200 mb-4">
          Generated Palette
        </h3>
        {paletteType !== 'all' ? (
          <div className="grid grid-cols-5 gap-4">
            {palette.map((color, index) => (
              <div
                key={index}
                className="group cursor-pointer"
                onClick={() => copyToClipboard(color)}
              >
                <div
                  className="w-full h-24 rounded-lg border border-zinc-200 dark:border-zinc-700 mb-2 group-hover:scale-105 transition-transform"
                  style={{ backgroundColor: color }}
                />
                <div className="text-center">
                  <div className="text-sm font-mono text-zinc-800 dark:text-zinc-200">
                    {color}
                  </div>
                  <div className="text-xs text-zinc-500 dark:text-zinc-400">
                    Click to copy
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="space-y-8">
            {allPalettes && Object.entries(allPalettes).map(([type, colors]) => (
              <div key={type}>
                <div className="mb-2 text-base font-semibold capitalize text-zinc-700 dark:text-zinc-300">{type} Palette</div>
                <div className="grid grid-cols-5 gap-4">
                  {colors.map((color, index) => (
                    <div
                      key={index}
                      className="group cursor-pointer"
                      onClick={() => copyToClipboard(color)}
                    >
                      <div
                        className="w-full h-24 rounded-lg border border-zinc-200 dark:border-zinc-700 mb-2 group-hover:scale-105 transition-transform"
                        style={{ backgroundColor: color }}
                      />
                      <div className="text-center">
                        <div className="text-sm font-mono text-zinc-800 dark:text-zinc-200">
                          {color}
                        </div>
                        <div className="text-xs text-zinc-500 dark:text-zinc-400">
                          Click to copy
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* CSS Output */}
      <div className="bg-zinc-50 dark:bg-zinc-800 rounded-lg p-4">
        <div className="text-xs font-semibold text-zinc-600 dark:text-zinc-400 mb-2">
          CSS Variables:
        </div>
        <code className="text-sm text-zinc-800 dark:text-zinc-200 font-mono block">
          {paletteType !== 'all'
            ? palette.map((color, index) => `--color-${index + 1}: ${color};`).join('\n')
            : allPalettes && Object.entries(allPalettes).map(([type, colors]) =>
                colors.map((color, index) => `--${type}-color-${index + 1}: ${color};`).join('\n')
              ).join('\n')}
        </code>
      </div>
    </div>
  );
} 